# Deep Linking Implementation

This document describes the deep linking implementation for the Shri Datt Mandir app using the `app_links` package with the custom scheme `shridattmandir://`.

## Overview

The app supports deep linking to various features, primarily focusing on the music module. Users can share and open specific content directly in the app through custom URLs.

## URL Scheme Structure

All deep links use the custom scheme: `shridattmandir://`

### Music Deep Links

#### Music Player
- **URL Pattern**: `shridattmandir://music/player/{musicId}`
- **Example**: `shridattmandir://music/player/abc123`
- **Description**: Opens a specific music track in the player
- **Fallback**: If musicId is invalid, redirects to music list

#### Music List
- **URL Pattern**: `shridattmandir://music/list`
- **Example**: `shridattmandir://music/list`
- **Description**: Opens the main music collection

#### Music Playlist/Favorites
- **URL Pattern**: `shridattmandir://music/playlist`
- **Example**: `shridattmandir://music/playlist`
- **Description**: Opens the user's favorite music collection

### Blog Deep Links

#### Blog View
- **URL Pattern**: `shridattmandir://blog/view/{blogId}`
- **Example**: `shridattmandir://blog/view/xyz789`
- **Description**: Opens a specific blog post
- **Fallback**: If blogId is invalid, redirects to blogs list

## Implementation Details

### Core Components

1. **DeepLinkUtils** (`lib/src/core/utils/deep_link_utils.dart`)
   - URL generation and parsing utilities
   - Validation functions
   - Shareable URL creation

2. **DeeplinkCubit** (`lib/src/feature/deeplink/cubit/deeplink_cubit.dart`)
   - Handles both Firebase notifications and app links
   - Premium subscription checks for music features
   - Navigation logic

3. **SdmShareUtils** (`lib/src/shared/sdm_share_utils.dart`)
   - Sharing utilities with deep link integration
   - Clipboard functionality
   - Pre-formatted share messages

### Platform Configuration

#### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="shridattmandir" />
</intent-filter>
```

#### iOS (`ios/Runner/Info.plist`)
```xml
<dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLName</key>
    <string>shridattmandir.deeplink</string>
    <key>CFBundleURLSchemes</key>
    <array>
        <string>shridattmandir</string>
    </array>
</dict>
```

## Usage Examples

### Generating Deep Links

```dart
// Generate music player link
final musicLink = DeepLinkUtils.generateMusicPlayerLink('music123');
// Result: shridattmandir://music/player/music123

// Generate music list link
final listLink = DeepLinkUtils.generateMusicListLink();
// Result: shridattmandir://music/list

// Generate blog link
final blogLink = DeepLinkUtils.generateBlogViewLink('blog456');
// Result: shridattmandir://blog/view/blog456
```

### Sharing Content

```dart
// Share a music track
await SdmShareUtils.shareMusicTrack(musicModel);

// Share music list
await SdmShareUtils.shareMusicList();

// Share playlist
await SdmShareUtils.shareMusicPlaylist();

// Share blog post
await SdmShareUtils.shareBlogPost(blogId, title);
```

### Parsing Deep Links

```dart
final linkData = DeepLinkUtils.parseDeepLink(url);
if (linkData != null) {
  final route = linkData['route'];
  final arguments = linkData['arguments'];
  // Navigate to the appropriate screen
}
```

## Security & Premium Features

- Music-related deep links check for premium subscription status
- Non-premium users are redirected to the paywall
- Invalid URLs gracefully fallback to appropriate screens
- Error handling prevents app crashes from malformed URLs

## Testing

Run the deep link tests to verify functionality:

```bash
flutter test test/deep_link_utils_test.dart
```

## Testing Deep Links

### Android Testing
```bash
# Test music player link
adb shell am start -W -a android.intent.action.VIEW -d "shridattmandir://music/player/test123" com.example.shridattmandir

# Test music list link
adb shell am start -W -a android.intent.action.VIEW -d "shridattmandir://music/list" com.example.shridattmandir
```

### iOS Testing
Use the iOS Simulator or device with Safari:
1. Open Safari
2. Enter the deep link URL in the address bar
3. The app should open automatically

## Share Message Format

When sharing content, the app generates formatted messages:

### Music Track
```
🎵 Listen to "Song Title" by Artist Name on Shri Datt Mandir app!

shridattmandir://music/player/musicId123

Download the app to enjoy spiritual music and more.
```

### Blog Post
```
📖 Read "Blog Title" on Shri Datt Mandir app!

shridattmandir://blog/view/blogId456

Download the app for spiritual content and more.
```

## Troubleshooting

1. **Deep links not working**: Ensure the app is installed and the URL scheme is correctly configured
2. **Premium content access**: Verify subscription status and paywall logic
3. **Invalid URLs**: Check URL format and ensure proper error handling
4. **Navigation issues**: Verify route names match the router configuration

## Future Enhancements

- Support for additional content types (videos, events, etc.)
- Dynamic link generation for better sharing
- Analytics tracking for deep link usage
- Custom domain support for web-based deep links
