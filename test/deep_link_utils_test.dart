import 'package:flutter_test/flutter_test.dart';
import 'package:shridattmandir/src/core/core.dart' show DeepLinkUtils, SdmRouter;

void main() {
  group('DeepLinkUtils', () {
    group('URL Generation', () {
      test('should generate correct music player link', () {
        const musicId = 'test_music_123';
        final result = DeepLinkUtils.generateMusicPlayerLink(musicId);
        expect(result, equals('shridattmandir://music/player/test_music_123'));
      });

      test('should generate correct music list link', () {
        final result = DeepLinkUtils.generateMusicListLink();
        expect(result, equals('shridattmandir://music/list'));
      });

      test('should generate correct music playlist link', () {
        final result = DeepLinkUtils.generateMusicPlaylistLink();
        expect(result, equals('shridattmandir://music/playlist'));
      });

      test('should generate correct blog view link', () {
        const blogId = 'test_blog_456';
        final result = DeepLinkUtils.generateBlogViewLink(blogId);
        expect(result, equals('shridattmandir://blog/view/test_blog_456'));
      });
    });

    group('URL Parsing', () {
      test('should parse music player link correctly', () {
        const url = 'shridattmandir://music/player/test_music_123';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.musicPlayer));
        expect(result['arguments'], equals('test_music_123'));
      });

      test('should parse music list link correctly', () {
        const url = 'shridattmandir://music/list';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.musicList));
        expect(result['arguments'], isNull);
      });

      test('should parse music playlist link correctly', () {
        const url = 'shridattmandir://music/playlist';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.musicPlaylist));
        expect(result['arguments'], isNull);
      });

      test('should parse blog view link correctly', () {
        const url = 'shridattmandir://blog/view/test_blog_456';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.blogView));
        expect(result['arguments'], equals('test_blog_456'));
      });

      test('should handle music player link without ID', () {
        const url = 'shridattmandir://music/player';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.musicList));
        expect(result['arguments'], isNull);
      });

      test('should handle blog view link without ID', () {
        const url = 'shridattmandir://blog/view';
        final result = DeepLinkUtils.parseDeepLink(url);
        
        expect(result, isNotNull);
        expect(result!['route'], equals(SdmRouter.blogs));
        expect(result['arguments'], isNull);
      });

      test('should return null for invalid scheme', () {
        const url = 'https://example.com/music/player/123';
        final result = DeepLinkUtils.parseDeepLink(url);
        expect(result, isNull);
      });

      test('should return null for invalid host', () {
        const url = 'shridattmandir://invalid/player/123';
        final result = DeepLinkUtils.parseDeepLink(url);
        expect(result, isNull);
      });

      test('should return null for invalid path', () {
        const url = 'shridattmandir://music/invalid/123';
        final result = DeepLinkUtils.parseDeepLink(url);
        expect(result, isNull);
      });

      test('should return null for malformed URL', () {
        const url = 'not-a-valid-url';
        final result = DeepLinkUtils.parseDeepLink(url);
        expect(result, isNull);
      });
    });

    group('URL Validation', () {
      test('should validate correct deep links', () {
        const validUrls = [
          'shridattmandir://music/player/123',
          'shridattmandir://music/list',
          'shridattmandir://music/playlist',
          'shridattmandir://blog/view/456',
        ];

        for (final url in validUrls) {
          expect(DeepLinkUtils.isValidDeepLink(url), isTrue, reason: 'URL: $url');
        }
      });

      test('should reject invalid deep links', () {
        const invalidUrls = [
          'https://example.com/music/player/123',
          'shridattmandir://invalid/player/123',
          'not-a-url',
          '',
        ];

        for (final url in invalidUrls) {
          expect(DeepLinkUtils.isValidDeepLink(url), isFalse, reason: 'URL: $url');
        }
      });
    });

    group('Shareable URLs', () {
      test('should return correct shareable music URL', () {
        const musicId = 'test_music_789';
        final result = DeepLinkUtils.getShareableMusicUrl(musicId);
        expect(result, equals('shridattmandir://music/player/test_music_789'));
      });

      test('should return correct shareable blog URL', () {
        const blogId = 'test_blog_101';
        final result = DeepLinkUtils.getShareableBlogUrl(blogId);
        expect(result, equals('shridattmandir://blog/view/test_blog_101'));
      });
    });
  });
}
