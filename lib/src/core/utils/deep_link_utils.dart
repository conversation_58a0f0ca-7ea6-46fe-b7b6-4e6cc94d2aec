import 'package:shridattmandir/src/core/core.dart' show SdmRouter;

/// Utility class for generating and parsing deep links
class DeepLinkUtils {
  static const String scheme = 'shridattmandir';
  static const String musicHost = 'music';
  static const String blogHost = 'blog';
  
  // Music paths
  static const String playerPath = 'player';
  static const String listPath = 'list';
  static const String playlistPath = 'playlist';
  
  // Blog paths
  static const String viewPath = 'view';

  /// Generate deep link URL for music player with specific music ID
  /// Example: shridattmandir://music/player/musicId123
  static String generateMusicPlayerLink(String musicId) {
    return '$scheme://$musicHost/$playerPath/$musicId';
  }

  /// Generate deep link URL for music list
  /// Example: shridattmandir://music/list
  static String generateMusicListLink() {
    return '$scheme://$musicHost/$listPath';
  }

  /// Generate deep link URL for music playlist/favorites
  /// Example: shridattmandir://music/playlist
  static String generateMusicPlaylistLink() {
    return '$scheme://$musicHost/$playlistPath';
  }

  /// Generate deep link URL for blog view with specific blog ID
  /// Example: shridattmandir://blog/view/blogId123
  static String generateBlogViewLink(String blogId) {
    return '$scheme://$blogHost/$viewPath/$blogId';
  }

  /// Parse deep link URL and return route information
  /// Returns a map with 'route' and 'arguments' keys
  static Map<String, dynamic>? parseDeepLink(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Validate scheme
      if (uri.scheme != scheme) {
        return null;
      }

      final host = uri.host;
      final pathSegments = uri.pathSegments;

      if (pathSegments.isEmpty) {
        return null;
      }

      switch (host) {
        case musicHost:
          return _parseMusicLink(pathSegments);
        case blogHost:
          return _parseBlogLink(pathSegments);
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Parse music-related deep links
  static Map<String, dynamic>? _parseMusicLink(List<String> pathSegments) {
    if (pathSegments.isEmpty) {
      return null;
    }

    final action = pathSegments[0];

    switch (action) {
      case playerPath:
        if (pathSegments.length >= 2) {
          final musicId = pathSegments[1];
          return {
            'route': SdmRouter.musicPlayer,
            'arguments': musicId,
          };
        }
        // If no music ID provided, go to music list
        return {
          'route': SdmRouter.musicList,
          'arguments': null,
        };

      case listPath:
        return {
          'route': SdmRouter.musicList,
          'arguments': null,
        };

      case playlistPath:
        return {
          'route': SdmRouter.musicPlaylist,
          'arguments': null,
        };

      default:
        return null;
    }
  }

  /// Parse blog-related deep links
  static Map<String, dynamic>? _parseBlogLink(List<String> pathSegments) {
    if (pathSegments.isEmpty) {
      return null;
    }

    final action = pathSegments[0];

    switch (action) {
      case viewPath:
        if (pathSegments.length >= 2) {
          final blogId = pathSegments[1];
          return {
            'route': SdmRouter.blogView,
            'arguments': blogId,
          };
        }
        // If no blog ID provided, go to blogs list
        return {
          'route': SdmRouter.blogs,
          'arguments': null,
        };

      default:
        return null;
    }
  }

  /// Validate if a URL is a valid deep link for this app
  static bool isValidDeepLink(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.scheme == scheme && 
             (uri.host == musicHost || uri.host == blogHost);
    } catch (e) {
      return false;
    }
  }

  /// Get shareable URL for music
  static String getShareableMusicUrl(String musicId) {
    return generateMusicPlayerLink(musicId);
  }

  /// Get shareable URL for blog
  static String getShareableBlogUrl(String blogId) {
    return generateBlogViewLink(blogId);
  }
}
