import 'package:flutter/services.dart';
import 'package:shridattmandir/src/core/core.dart' show DeepLinkUtils;
import 'package:shridattmandir/src/feature/feature.dart' show MusicModel;
import 'package:shridattmandir/src/shared/shared.dart' show SdmToast;

/// Utility class for sharing content with deep links
class SdmShareUtils {
  /// Share a music track with deep link
  static Future<void> shareMusicTrack(MusicModel music) async {
    if (music.documentId == null) {
      SdmToast.show('Cannot share this music track');
      return;
    }

    final deepLink = DeepLinkUtils.generateMusicPlayerLink(music.documentId!);
    final shareText = _buildMusicShareText(music, deepLink);
    
    await _copyToClipboard(shareText);
    SdmToast.show('Music link copied to clipboard!');
  }

  /// Share music list
  static Future<void> shareMusicList() async {
    final deepLink = DeepLinkUtils.generateMusicListLink();
    const shareText = 'Check out the music collection on Shri Datt Mandir app!';
    
    await _copyToClipboard('$shareText\n\n$deepLink');
    SdmToast.show('Music list link copied to clipboard!');
  }

  /// Share music playlist/favorites
  static Future<void> shareMusicPlaylist() async {
    final deepLink = DeepLinkUtils.generateMusicPlaylistLink();
    const shareText = 'Check out my favorite music on Shri Datt Mandir app!';
    
    await _copyToClipboard('$shareText\n\n$deepLink');
    SdmToast.show('Playlist link copied to clipboard!');
  }

  /// Share a blog post with deep link
  static Future<void> shareBlogPost(String blogId, String? title) async {
    final deepLink = DeepLinkUtils.generateBlogViewLink(blogId);
    final shareText = _buildBlogShareText(title, deepLink);
    
    await _copyToClipboard(shareText);
    SdmToast.show('Blog link copied to clipboard!');
  }

  /// Build share text for music
  static String _buildMusicShareText(MusicModel music, String deepLink) {
    final title = music.title ?? 'Music Track';
    final artist = music.lyricist ?? 'Shri Datt Mandir';
    
    return '''🎵 Listen to "$title" by $artist on Shri Datt Mandir app!

$deepLink

Download the app to enjoy spiritual music and more.''';
  }

  /// Build share text for blog
  static String _buildBlogShareText(String? title, String deepLink) {
    final blogTitle = title ?? 'Blog Post';
    
    return '''📖 Read "$blogTitle" on Shri Datt Mandir app!

$deepLink

Download the app for spiritual content and more.''';
  }

  /// Copy text to clipboard
  static Future<void> _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }

  /// Get app download message for sharing
  static String getAppDownloadMessage() {
    return '''🙏 Download Shri Datt Mandir app for:
• Spiritual Music Collection
• Daily Quotes & Teachings  
• Event Calendar
• Blog Posts & Articles
• And much more!

Available on App Store and Google Play.''';
  }
}
