import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show
        BlogsRepository,
        FlavorConfig,
        MusicRepository,
        PurchasesRepository,
        SubscriptionService,
        kEntitlementId;
import 'package:shridattmandir/src/shared/shared.dart';

part 'deeplink_state.dart';

class DeeplinkCubit extends Cubit<DeeplinkState> {
  DeeplinkCubit({
    required PurchasesRepository purchasesRepository,
    required FirebaseMessaging firebaseMessaging,
    required MusicRepository musicRepository,
    required BlogsRepository blogsRepository,
    SubscriptionService? subscriptionService,
  })  : _purchasesRepository = purchasesRepository,
        _firebaseMessaging = firebaseMessaging,
        _musicRepository = musicRepository,
        _blogsRepository = blogsRepository,
        _subscriptionService = subscriptionService,
        super(const DeeplinkState());

  final PurchasesRepository _purchasesRepository;
  final FirebaseMessaging _firebaseMessaging;
  final MusicRepository _musicRepository;
  final BlogsRepository _blogsRepository;
  final SubscriptionService? _subscriptionService;

  late final AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  final String routeName = 'route';
  final String arguments = 'arguments';

  Future<void> initialize() async {
    await _requestPermission();

    if (await FlutterAppBadger.isAppBadgeSupported()) {
      await FlutterAppBadger.removeBadge();
    }

    // Initialize app links
    _appLinks = AppLinks();
    await _initializeAppLinks();

    FirebaseMessaging.onMessage.listen(
      (message) {
        safeEmit(
          state.copyWith(
            messageOnForeground: true,
            remoteMessage: () => message,
          ),
        );
      },
    );

    FirebaseMessaging.onMessageOpenedApp.listen(
      (message) async {
        safeEmit(
          state.copyWith(
            messageOnForeground: false,
            remoteMessage: () => message,
          ),
        );
        await handleNotification(message);
      },
    );

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      safeEmit(
        state.copyWith(
          messageOnForeground: false,
          remoteMessage: () => initialMessage,
        ),
      );
      await handleNotification(initialMessage);
    }
  }

  Future<void> _requestPermission() async {
    final permission = await _firebaseMessaging.requestPermission(
      sound: true,
      badge: true,
      alert: true,
    );

    if (permission.alert == AppleNotificationSetting.enabled) {
      _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
    debugPrint(await _firebaseMessaging.getToken());
  }

  Future<void> handleNotification(RemoteMessage message) async {
    safeEmit(
      state.copyWith(
        messageOnForeground: false,
        remoteMessage: () => message,
      ),
    );

    final data = message.data;

    if (data.isEmpty) return;

    final route = data[routeName];
    final argumentsData = data[arguments];

    if (route == null) return;

    bool hasMusicPremium = true;

    if (FlavorConfig.isProduction()) {
      if (_subscriptionService != null) {
        hasMusicPremium = _subscriptionService.hasMusicPremium;
      } else {
        // Fallback to direct repository call if service not available
        final customerInfo = await _purchasesRepository.getCustomerInfo();
        hasMusicPremium =
            customerInfo.entitlements.all[kEntitlementId]?.isActive ?? false;
      }
    }

    switch (route) {
      case SdmRouter.musicPlayer:
        if (!hasMusicPremium) {
          await navigate(
            routeName: SdmRouter.paywall,
          );
          return;
        }
        if (argumentsData == null) {
          await navigate(
            routeName: SdmRouter.musicList,
          );
          return;
        }

        try {
          final musicById =
              await _musicRepository.fetchMusicById(id: argumentsData);

          if (musicById == null) {
            await navigate(
              routeName: SdmRouter.musicList,
            );
            return;
          }

          await navigate(
            routeName: route,
            arguments: MusicPlayerArguments(
              musicList: [musicById],
            ),
          );
        } catch (e) {
          SdmToast.show(e.toString());
        }
        break;
      case SdmRouter.musicList:
        if (!hasMusicPremium) {
          await navigate(
            routeName: SdmRouter.paywall,
          );
          return;
        }
        await navigate(
          routeName: route,
        );

        break;

      case SdmRouter.blogView:
        try {
          final blogById =
              await _blogsRepository.fetchBlogById(id: argumentsData);

          await navigate(
            routeName: route,
            arguments: blogById,
          );
        } catch (e) {
          SdmToast.show(e.toString());
        }
        break;

      default:
        await navigate(
          routeName: route,
        );
    }
  }

  Future<void> navigate({
    required String routeName,
    Object? arguments,
  }) async {
    safeEmit(
      state.copyWith(
        routeName: () => routeName,
        arguments: () => arguments,
      ),
    );
  }

  /// Initialize app links for deep linking
  Future<void> _initializeAppLinks() async {
    try {
      // Handle initial link when app is launched from a deep link
      final initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        await _handleAppLink(initialUri);
      }

      // Listen for incoming links when app is already running
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (uri) async {
          await _handleAppLink(uri);
        },
        onError: (err) {
          debugPrint('Deep link error: $err');
        },
      );
    } catch (e) {
      debugPrint('Failed to initialize app links: $e');
    }
  }

  /// Handle incoming app link
  Future<void> _handleAppLink(Uri uri) async {
    try {
      final url = uri.toString();
      debugPrint('Received deep link: $url');

      // Update state with the app link URL
      safeEmit(
        state.copyWith(
          appLinkUrl: () => url,
        ),
      );

      // Parse the deep link and navigate
      final linkData = DeepLinkUtils.parseDeepLink(url);
      if (linkData != null) {
        final route = linkData['route'] as String;
        final arguments = linkData['arguments'];

        // Check premium access for music routes
        bool hasMusicPremium = true;
        if (route == SdmRouter.musicPlayer ||
            route == SdmRouter.musicList ||
            route == SdmRouter.musicPlaylist) {
          if (FlavorConfig.isProduction()) {
            if (_subscriptionService != null) {
              hasMusicPremium = _subscriptionService.hasMusicPremium;
            } else {
              // Fallback to direct repository call if service not available
              final customerInfo = await _purchasesRepository.getCustomerInfo();
              hasMusicPremium =
                  customerInfo.entitlements.all[kEntitlementId]?.isActive ??
                      false;
            }
          }

          if (!hasMusicPremium) {
            await navigate(routeName: SdmRouter.paywall);
            return;
          }
        }

        // Handle specific routes
        if (route == SdmRouter.musicPlayer && arguments != null) {
          try {
            final musicById =
                await _musicRepository.fetchMusicById(id: arguments);
            if (musicById == null) {
              await navigate(routeName: SdmRouter.musicList);
              return;
            }

            await navigate(
              routeName: route,
              arguments: MusicPlayerArguments(
                musicList: [musicById],
              ),
            );
          } catch (e) {
            SdmToast.show(e.toString());
            await navigate(routeName: SdmRouter.musicList);
          }
        } else if (route == SdmRouter.blogView && arguments != null) {
          try {
            final blogById =
                await _blogsRepository.fetchBlogById(id: arguments);
            await navigate(
              routeName: route,
              arguments: blogById,
            );
          } catch (e) {
            SdmToast.show(e.toString());
            await navigate(routeName: SdmRouter.blogs);
          }
        } else {
          await navigate(
            routeName: route,
            arguments: arguments,
          );
        }
      } else {
        debugPrint('Invalid deep link format: $url');
        // Navigate to home for invalid links
        await navigate(routeName: SdmRouter.home);
      }
    } catch (e) {
      debugPrint('Error handling app link: $e');
      SdmToast.show('Failed to open link');
    }
  }

  void reset() {
    safeEmit(
      state.copyWith(
        routeName: () => null,
        arguments: () => null,
        appLinkUrl: () => null,
      ),
    );
  }

  @override
  Future<void> close() {
    _linkSubscription?.cancel();
    return super.close();
  }
}
