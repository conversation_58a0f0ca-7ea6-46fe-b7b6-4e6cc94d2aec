import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart'
    show MusicPlayerArguments, SdmPalette, SdmRouter, SdmUrls;
import 'package:shridattmandir/src/feature/feature.dart'
    show
        AnalyticsCubit,
        MusicLyricsScreen,
        MusicPlayEvent,
        MusicPlayerCubit,
        MusicPlayerState,
        PlayerButton,
        PlayerButtonState,
        UserSessionCubit;
import 'package:shridattmandir/src/feature/music/music.dart';
import 'package:shridattmandir/src/shared/shared.dart'
    show
        SdmLaunchUrl,
        SdmNetworkImage,
        SdmShimmerLoader,
        SdmShareUtils,
        GenericPopupMenu,
        PopupItem;

class MusicPlayer extends StatefulWidget {
  const MusicPlayer({
    super.key,
    this.musicPlayerArgs,
  });

  final MusicPlayerArguments? musicPlayerArgs;

  @override
  State<MusicPlayer> createState() => _MusicPlayerState();
}

class _MusicPlayerState extends State<MusicPlayer> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        await context.read<MusicPlayerCubit>().initializeAudioPlayer(
              musicList: widget.musicPlayerArgs?.musicList,
              lastVisibleDocumentSnapshot:
                  widget.musicPlayerArgs?.documentSnapshot,
              index: widget.musicPlayerArgs?.index ?? 0,
              limit: widget.musicPlayerArgs?.limit ?? 10,
            );
      },
    );
  }

  final List<PopupItem<LoopModeEnum>> loopModeItems = const [
    PopupItem(value: LoopModeEnum.once, label: 'Play Once'),
    PopupItem(value: LoopModeEnum.loop, label: 'Loop current song'),
    PopupItem(value: LoopModeEnum.playlist, label: 'Auto play'),
  ];

  final List<PopupItem<double>> speedItems = const [
    PopupItem(value: 1.0, label: 'Normal (1.0x)'),
    PopupItem(value: 1.25, label: '1.25x'),
    PopupItem(value: 1.5, label: '1.5x'),
    PopupItem(value: 2.0, label: '2.0x'),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MusicPlayerCubit, MusicPlayerState>(
      listenWhen: (previous, current) =>
          previous.currentMusic?.documentId != current.currentMusic?.documentId,
      listener: (context, state) {
        final analyticsCubit = context.read<AnalyticsCubit>();
        analyticsCubit.onTrackAnalyticsEvent(
          MusicPlayEvent(
              musicName: state.currentMusic?.title ?? '',
              id: state.currentMusic?.documentId ?? ''),
        );
      },
      builder: (context, state) {
        final userSessionState = context.watch<UserSessionCubit>().state;

        return Scaffold(
          appBar: AppBar(
            iconTheme: const IconThemeData(
              color: SdmPalette.black,
            ),
            title: const Text(
              'Playing',
            ),
            actions: [
              if (userSessionState.userProfile?.isAdmin ?? false) ...[
                IconButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    if (state.currentMusic?.audioUrl != null) {
                      SdmLaunchUrl.sdmLaunchUrl(
                        state.currentMusic!.audioUrl!,
                      );
                    }
                  },
                  icon: const Icon(
                    Icons.cloud_download_outlined,
                    color: SdmPalette.primary,
                  ),
                ),
                IconButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      SdmRouter.musicInput,
                      arguments: state.currentMusic,
                    );
                  },
                  icon: const Icon(
                    Icons.edit,
                    color: SdmPalette.primary,
                  ),
                ),
              ],
              // Share button
              IconButton(
                onPressed: () async {
                  if (state.currentMusic != null) {
                    await SdmShareUtils.shareMusicTrack(state.currentMusic!);
                  }
                },
                icon: const Icon(
                  Icons.share,
                  color: SdmPalette.primary,
                ),
                tooltip: 'Share',
              ),
              TextButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: SdmPalette.transparent,
                    isScrollControlled: true,
                    enableDrag: true,
                    builder: (context) => BlocProvider.value(
                      value: context.read<MusicPlayerCubit>(),
                      child: const MusicLyricsScreen(),
                    ),
                  );
                },
                child: Text(
                  'Lyrics',
                  style: TextStyle(
                    color: SdmPalette.lightRed,
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ],
            centerTitle: true,
          ),
          body: Padding(
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
            ).w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 70.h,
                ),
                SdmNetworkImage(
                  shape: BoxShape.circle,
                  url: ((state.currentMusic?.artUrl != null) &&
                          state.currentMusic!.artUrl!.isNotEmpty)
                      ? state.currentMusic?.artUrl
                      : SdmUrls.kMandirLogo,
                  height: 220.w,
                  width: 220.w,
                  placeHolderWidget: SdmShimmerLoader(
                    child: Container(
                      height: 220.w,
                      width: 220.w,
                      decoration: BoxDecoration(
                        color: SdmPalette.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Text(
                  state.playlistChildren?[state.playlistCurrentIndex].title ??
                      '',
                  style: TextStyle(
                    color: SdmPalette.black,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 4.h,
                ),
                Text(
                  state.currentMusic?.lyricist ?? '',
                  style: TextStyle(
                    color: SdmPalette.textColorGrey2,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Spacer(),
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 20.0).w,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GenericPopupMenu<LoopModeEnum>(
                          items: loopModeItems,
                          onSelected: (mode) async {
                            await context
                                .read<MusicPlayerCubit>()
                                .switchLoopMode(
                                    loopMode: mode, setByUser: true);
                          },
                          icon: state.loopMode == LoopModeEnum.once
                              ? Icons.play_disabled
                              : state.loopMode == LoopModeEnum.loop
                                  ? Icons.repeat_one
                                  : Icons.repeat,
                          tooltip: 'Change loop mode',
                          iconColor: SdmPalette.progressBarGrey,
                        ),
                        GenericPopupMenu<double>(
                          items: speedItems,
                          onSelected: (speed) async {
                            await context
                                .read<MusicPlayerCubit>()
                                .setPlaybackSpeed(speed);
                          },
                          icon: Icons.speed,
                          tooltip: 'Change playback speed',
                          iconColor: state.playbackSpeed > 1.0
                              ? SdmPalette.primary
                              : SdmPalette.progressBarGrey,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0).w,
                  child: IgnorePointer(
                    ignoring:
                        state.playerButtonState == PlayerButtonState.loading,
                    child: ProgressBar(
                      onSeek: context.read<MusicPlayerCubit>().seek,
                      progress: state.positionDuration ?? const Duration(),
                      buffered: state.bufferedDuration,
                      total: state.totalDuration ?? const Duration(),
                      progressBarColor: SdmPalette.primary,
                      bufferedBarColor: SdmPalette.white.withValues(alpha: .4),
                      thumbColor: SdmPalette.white,
                      barHeight: 3.0.h,
                      thumbRadius: 8.0.r,
                      baseBarColor: SdmPalette.textColorGrey2,
                      timeLabelTextStyle: TextStyle(
                        color: SdmPalette.progressBarGrey,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    const Spacer(),
                    //previous button
                    IconButton(
                      icon: const Icon(Icons.skip_previous),
                      iconSize: 30.w,
                      color: SdmPalette.progressBarGrey,
                      onPressed: () async {
                        await context.read<MusicPlayerCubit>().seekToPrevious();
                      },
                    ),
                    SizedBox(
                      width: 24.w,
                    ),
                    const PlayerButton(),
                    SizedBox(
                      width: 24.w,
                    ),
                    //next button
                    IconButton(
                      icon: const Icon(Icons.skip_next),
                      iconSize: 30.w,
                      color: SdmPalette.progressBarGrey,
                      onPressed: () async {
                        await context.read<MusicPlayerCubit>().seekToNext(
                              force: true,
                            );
                      },
                    ),
                    const Spacer(),
                  ],
                ),
                const Spacer(),
              ],
            ),
          ),
        );
      },
    );
  }
}
